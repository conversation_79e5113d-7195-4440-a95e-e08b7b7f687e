#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国经济指标数据收集器

该脚本收集以下美国经济指标：
1. 美国OECD领先指标 (OECD Leading Indicators)
2. 美国个人消费支出：服务 (Personal Consumption Expenditures: Services)
3. 美国Markit服务业PMI (Markit Services PMI)
4. 美国Markit制造业PMI (Markit Manufacturing PMI)

数据来源:
- FRED (Federal Reserve Economic Data) - 主要数据源
- OECD API - OECD领先指标
- BEA API - 个人消费支出数据
- akshare - PMI数据备用源
"""

import pandas as pd
import pandas_datareader.data as pdr
import os
from datetime import datetime, timedelta
import warnings
import time
import requests
import io
from dateutil.relativedelta import relativedelta

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

def calculate_release_date(data_date, indicator_name):
    """
    计算经济指标的发布日期

    Args:
        data_date (datetime): 数据期间日期
        indicator_name (str): 指标名称

    Returns:
        datetime: 估计的发布日期

    发布规则:
    - OECD领先指标: 下个月第一周的周五发布这个月的数据
    - 个人消费支出服务: 下个月月末发布这个月的数据
    - 服务业PMI: 这个月的月末发布这个月的数据
    - 制造业PMI: 这个月的月末发布这个月的数据
    """
    import calendar

    try:
        if isinstance(data_date, str):
            data_date = pd.to_datetime(data_date)

        if 'OECD' in indicator_name or 'oecd' in indicator_name:
            # OECD领先指标: 下个月第一周的周五
            next_month = data_date + relativedelta(months=1)
            # 找到下个月第一周的周五
            first_day = next_month.replace(day=1)
            # 找到第一个周五 (weekday() 返回 0=Monday, 4=Friday)
            days_until_friday = (4 - first_day.weekday()) % 7
            if days_until_friday == 0 and first_day.weekday() != 4:
                days_until_friday = 7
            first_friday = first_day + relativedelta(days=days_until_friday)
            return first_friday

        elif '个人消费支出' in indicator_name or 'pce' in indicator_name.lower():
            # 个人消费支出服务: 下个月月末
            next_month = data_date + relativedelta(months=1)
            # 获取下个月的最后一天
            last_day = calendar.monthrange(next_month.year, next_month.month)[1]
            return next_month.replace(day=last_day)

        elif 'PMI' in indicator_name or 'pmi' in indicator_name.lower():
            # 服务业PMI和制造业PMI: 这个月的月末
            # 获取当月的最后一天
            last_day = calendar.monthrange(data_date.year, data_date.month)[1]
            return data_date.replace(day=last_day)

        else:
            # 默认规则：下个月15日
            release_date = data_date + relativedelta(months=1)
            return release_date.replace(day=15)

    except Exception as e:
        print(f"计算发布日期时出错: {e}")
        # 返回默认值：数据日期后1个月的15日
        return data_date + relativedelta(months=1, day=15)

def add_release_dates(df, indicator_name):
    """
    为数据框添加发布日期列

    Args:
        df (DataFrame): 包含经济指标数据的数据框
        indicator_name (str): 指标名称，用于确定发布规则

    Returns:
        DataFrame: 添加了发布日期的数据框
    """
    if df.empty:
        return df

    df_with_release = df.copy()

    # 计算发布日期
    df_with_release['发布日期'] = df_with_release.index.map(
        lambda x: calculate_release_date(x, indicator_name)
    )

    return df_with_release


def calculate_pmi_release_date(data_date):
    """
    计算PMI数据的发布日期，区分初值和终值

    Markit PMI发布规律：
    - 初值：每月第一个工作日左右（通常1-3日）
    - 终值：每月下旬（通常22-24日）

    Args:
        data_date (datetime): 数据日期

    Returns:
        datetime: 发布日期
    """
    import calendar

    try:
        # 根据数据日期的天数判断是初值还是终值
        day = data_date.day

        if day <= 5:
            # 初值：当月第一个工作日左右发布
            # 简化处理：当月3日发布
            return data_date.replace(day=3)
        else:
            # 终值：当月下旬发布
            # 简化处理：当月23日发布
            return data_date.replace(day=23)

    except Exception as e:
        print(f"计算PMI发布日期时出错: {e}")
        # 返回默认值：当月月末
        last_day = calendar.monthrange(data_date.year, data_date.month)[1]
        return data_date.replace(day=last_day)


def add_pmi_release_dates(df, indicator_name):
    """
    为PMI数据添加发布日期列，区分初值和终值的发布日期

    Args:
        df (DataFrame): 包含PMI数据的DataFrame
        indicator_name (str): 指标名称

    Returns:
        DataFrame: 添加了发布日期的DataFrame
    """
    if df.empty:
        return df

    df_with_release = df.copy()

    # 计算PMI发布日期
    df_with_release['发布日期'] = df_with_release.index.map(calculate_pmi_release_date)

    return df_with_release


# API配置
try:
    # 尝试从config.py文件读取API密钥
    from config import BEA_API_KEY, API_CONFIG
    print("✓ 从config.py加载API配置")
except ImportError:
    # 如果没有config.py，尝试从环境变量读取
    BEA_API_KEY = os.getenv('BEA_API_KEY', None)
    API_CONFIG = {
        'BEA': {
            'api_key': BEA_API_KEY,
            'base_url': 'https://apps.bea.gov/api/data',
            'timeout': 30
        }
    }
    if BEA_API_KEY:
        print("✓ 从环境变量加载BEA API密钥")
    else:
        print("⚠️  未找到BEA API密钥")
        print("   请复制config_template.py为config.py并填入API密钥")
        print("   或设置环境变量: export BEA_API_KEY=your_api_key")

def collect_oecd_leading_indicator(start_date=None, end_date=None):
    """
    收集美国OECD领先指标数据

    Args:
        start_date (str): 开始日期，格式为'YYYY-MM-DD'
        end_date (str): 结束日期，格式为'YYYY-MM-DD'

    Returns:
        DataFrame: OECD领先指标数据
    """
    print("正在收集美国OECD领先指标数据...")

    # 设置默认日期范围
    if start_date is None:
        start_year = '2000'
    else:
        start_year = start_date[:4]

    # 首先尝试使用OECD SDMX API
    try:
        print("  尝试使用OECD SDMX API...")
        # OECD SDMX API URL for US CLI data
        # USA.M.LI...AA...H - United States, Monthly, Leading Indicator, Amplitude Adjusted
        oecd_cli_url = f"https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_CLI/USA.M.LI...AA...H?startPeriod={start_year}-01&dimensionAtObservation=AllDimensions&format=csvfilewithlabels"

        print(f"  从OECD API获取数据: {oecd_cli_url}")

        # 获取数据
        response = requests.get(oecd_cli_url, timeout=30)
        response.raise_for_status()

        # 读取CSV数据
        cli_df = pd.read_csv(io.StringIO(response.text))

        print(f"  OECD数据列: {cli_df.columns.tolist()}")

        # 处理数据
        if 'TIME_PERIOD' in cli_df.columns and 'OBS_VALUE' in cli_df.columns:
            # 提取相关列
            cli_data = cli_df[['TIME_PERIOD', 'OBS_VALUE']].copy()
            cli_data = cli_data.dropna()

            # 转换时间格式
            cli_data['Date'] = pd.to_datetime(cli_data['TIME_PERIOD'])
            cli_data = cli_data.set_index('Date')
            cli_data = cli_data.rename(columns={'OBS_VALUE': 'OECD领先指标'})
            cli_data = cli_data[['OECD领先指标']]

            # 按日期排序
            cli_data = cli_data.sort_index()

            # 转换为数值类型
            cli_data['OECD领先指标'] = pd.to_numeric(cli_data['OECD领先指标'], errors='coerce')
            cli_data = cli_data.dropna()

            if len(cli_data) > 0:
                print(f"✓ 成功从OECD API获取领先指标数据: {len(cli_data)} 条记录")
                print(f"  数据范围: {cli_data.index.min().strftime('%Y-%m-%d')} 到 {cli_data.index.max().strftime('%Y-%m-%d')}")
                print(f"  最新值: {cli_data['OECD领先指标'].iloc[-1]:.2f}")

                # 添加发布日期
                cli_data_with_release = add_release_dates(cli_data, 'OECD领先指标')
                print(f"  已添加发布日期信息")
                return cli_data_with_release
            else:
                print("  OECD API返回的数据为空")
        else:
            print(f"  OECD API数据格式不符合预期，列名: {cli_df.columns.tolist()}")

    except Exception as e:
        print(f"  OECD API获取失败: {str(e)}")

    # 如果OECD API失败，回退到FRED
    print("  回退到FRED数据源...")
    try:
        # FRED series ID for OECD Leading Indicators for the United States
        series_id = 'USALOLITONOSTSAM'  # OECD based Composite of Leading Indicators: Normalized for the United States

        # 从FRED获取数据
        df = pdr.get_data_fred(series_id, start=start_date, end=end_date)

        # 重命名列
        df.columns = ['OECD领先指标']

        # 移除缺失值
        df = df.dropna()

        if len(df) > 0:
            print(f"✓ 成功从FRED获取OECD领先指标数据: {len(df)} 条记录")
            print(f"  数据范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  最新值: {df['OECD领先指标'].iloc[-1]:.2f}")

            # 添加发布日期
            df_with_release = add_release_dates(df, 'OECD领先指标')
            print(f"  已添加发布日期信息")
            return df_with_release

        return df

    except Exception as e:
        print(f"✗ FRED获取OECD领先指标数据也失败: {str(e)}")
        return pd.DataFrame()

def collect_pce_services(start_date=None, end_date=None):
    """
    收集美国个人消费支出：服务数据

    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期

    Returns:
        DataFrame: 个人消费支出服务数据
    """
    print("正在收集美国个人消费支出：服务数据...")

    # 设置默认日期范围
    if start_date is None:
        start_year = '2000'
    else:
        start_year = start_date[:4]

    # 首先尝试使用BEA API
    if BEA_API_KEY:
        try:
            print("  尝试使用BEA API...")

            # BEA API参数
            # Table 2.8.4 - Personal Consumption Expenditures by Major Type of Product, Monthly
            bea_api_url = "https://apps.bea.gov/api/data"

            # 构建请求参数
            params = {
                'UserID': BEA_API_KEY,
                'method': 'GetData',
                'datasetname': 'NIPA',
                'TableName': 'T20805',  # Table 2.8.5 - PCE by Major Type of Product, Monthly (实际金额)
                'Frequency': 'M',  # Monthly
                'Year': 'X',  # All years
                'ResultFormat': 'json'
            }

            print(f"  从BEA API获取数据: {bea_api_url}")

            # 发送请求
            response = requests.get(bea_api_url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            print(f"  BEA API响应状态: {response.status_code}")

            # 检查API响应
            if 'BEAAPI' in data and 'Results' in data['BEAAPI']:
                results = data['BEAAPI']['Results']

                # 检查是否有错误
                if 'Error' in results:
                    error_info = results['Error']
                    print(f"  BEA API错误: {error_info}")
                elif 'Data' in results:
                    bea_data = results['Data']
                    print(f"  BEA API返回 {len(bea_data)} 条数据记录")

                    # 转换为DataFrame
                    df_bea = pd.DataFrame(bea_data)

                    # 筛选服务数据 - 只要总的"Services"行，不要细分类别
                    if 'LineDescription' in df_bea.columns:
                        # 只查找精确匹配"Services"的行，排除细分类别
                        services_mask = (df_bea['LineDescription'] == 'Services') | \
                                      (df_bea['LineDescription'] == 'Personal consumption expenditures: Services')
                        services_data = df_bea[services_mask].copy()

                        if not services_data.empty:
                            print(f"  找到 {len(services_data)} 条服务相关数据")

                            # 处理数据 - 修复BEA日期格式解析
                            def parse_bea_date(date_str):
                                """解析BEA日期格式 2023M01 -> 2023-01-01"""
                                try:
                                    year, month = date_str.split('M')
                                    return pd.to_datetime(f"{year}-{month.zfill(2)}-01")
                                except:
                                    return pd.NaT

                            services_data['Date'] = services_data['TimePeriod'].apply(parse_bea_date)

                            # 处理数值并检查单位
                            services_data['Value'] = pd.to_numeric(
                                services_data['DataValue'].str.replace(',', ''),
                                errors='coerce'
                            )

                            # 检查单位倍数并转换为十亿美元
                            unit_mult = services_data['UNIT_MULT'].iloc[0] if not services_data.empty else '0'
                            if unit_mult == '6':  # 百万美元
                                services_data['Value'] = services_data['Value'] / 1000  # 转换为十亿美元
                                print(f"  数据单位已转换：百万美元 → 十亿美元")

                            # 创建时间序列
                            pce_services = services_data.set_index('Date')[['Value']].copy()
                            pce_services.columns = ['个人消费支出_服务']
                            pce_services = pce_services.sort_index()
                            pce_services = pce_services.dropna()

                            # 筛选时间范围
                            if start_date:
                                pce_services = pce_services[pce_services.index >= start_date]
                            if end_date:
                                pce_services = pce_services[pce_services.index <= end_date]

                            if len(pce_services) > 0:
                                print(f"✓ 成功从BEA API获取个人消费支出服务数据: {len(pce_services)} 条记录")
                                print(f"  数据范围: {pce_services.index.min().strftime('%Y-%m-%d')} 到 {pce_services.index.max().strftime('%Y-%m-%d')}")
                                print(f"  最新值: {pce_services['个人消费支出_服务'].iloc[-1]:.2f} 十亿美元")

                                # 计算同比增长率
                                if len(pce_services) >= 12:
                                    pce_services['个人消费支出_服务_同比'] = pce_services['个人消费支出_服务'].pct_change(12) * 100

                                # 添加发布日期
                                pce_services_with_release = add_release_dates(pce_services, '个人消费支出_服务')
                                print(f"  已添加发布日期信息")
                                return pce_services_with_release
                            else:
                                print("  BEA API返回的数据为空")
                        else:
                            print("  BEA API中未找到服务数据")
                    else:
                        print("  BEA数据中无LineDescription列")
                else:
                    print("  BEA API Results中无Data字段")
            else:
                print("  BEA API响应格式异常")

        except Exception as e:
            print(f"  BEA API获取失败: {str(e)}")
    else:
        print("  跳过BEA API（未设置API密钥）")
        print("  请在 https://apps.bea.gov/API/signup/ 注册获取API密钥")

    # 如果BEA API失败，回退到FRED
    print("  回退到FRED数据源...")
    try:
        # FRED series ID for Personal Consumption Expenditures: Services
        series_id = 'PCESV'  # Personal Consumption Expenditures: Services

        # 从FRED获取数据
        df = pdr.get_data_fred(series_id, start=start_date, end=end_date)

        # 重命名列
        df.columns = ['个人消费支出_服务']

        # 移除缺失值
        df = df.dropna()

        if len(df) > 0:
            print(f"✓ 成功从FRED获取个人消费支出服务数据: {len(df)} 条记录")
            print(f"  数据范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  最新值: {df['个人消费支出_服务'].iloc[-1]:.2f} 十亿美元")

            # 计算同比增长率
            if len(df) >= 12:
                df['个人消费支出_服务_同比'] = df['个人消费支出_服务'].pct_change(12) * 100

            # 添加发布日期
            df_with_release = add_release_dates(df, '个人消费支出_服务')
            print(f"  已添加发布日期信息")
            return df_with_release

        return df

    except Exception as e:
        print(f"✗ FRED获取个人消费支出服务数据也失败: {str(e)}")
        return pd.DataFrame()

def collect_services_pmi(start_date=None, end_date=None):
    """
    收集美国Markit服务业PMI数据 - 直接从akshare获取

    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期

    Returns:
        DataFrame: Markit服务业PMI数据
    """
    print("正在收集美国Markit服务业PMI数据...")
    print("  使用akshare获取Markit服务业PMI数据...")

    try:
        import akshare as ak

        # 使用akshare获取美国Markit服务业PMI初值报告
        services_pmi = ak.macro_usa_services_pmi()

        if not services_pmi.empty:
            print(f"✓ 成功从akshare获取Markit服务业PMI数据: {len(services_pmi)} 条记录")

            # 处理数据格式
            services_pmi['日期'] = pd.to_datetime(services_pmi['日期'])
            services_pmi = services_pmi.set_index('日期')
            services_pmi = services_pmi.rename(columns={'今值': 'Markit服务业PMI'})
            services_pmi['Markit服务业PMI'] = pd.to_numeric(services_pmi['Markit服务业PMI'], errors='coerce')
            services_pmi = services_pmi[['Markit服务业PMI']].dropna()

            # 按日期排序
            services_pmi = services_pmi.sort_index()

            # 筛选时间范围
            if start_date:
                services_pmi = services_pmi[services_pmi.index >= start_date]
            if end_date:
                services_pmi = services_pmi[services_pmi.index <= end_date]

            if len(services_pmi) > 0:
                print(f"  数据范围: {services_pmi.index.min().strftime('%Y-%m-%d')} 到 {services_pmi.index.max().strftime('%Y-%m-%d')}")
                print(f"  最新值: {services_pmi['Markit服务业PMI'].iloc[-1]:.1f}")

                # akshare返回的日期就是发布日期，直接使用
                services_pmi_with_release = services_pmi.copy()
                services_pmi_with_release['发布日期'] = services_pmi_with_release.index
                print(f"  已使用实际发布日期（akshare日期字段就是发布日期）")
                return services_pmi_with_release
            else:
                print("  筛选后数据为空")
        else:
            print("✗ akshare返回的Markit服务业PMI数据为空")

    except ImportError:
        print("✗ akshare未安装，无法获取Markit服务业PMI数据")
    except Exception as e:
        print(f"✗ akshare获取Markit服务业PMI数据失败: {str(e)}")

    return pd.DataFrame()

def collect_manufacturing_pmi(start_date=None, end_date=None):
    """
    收集美国Markit制造业PMI数据 - 直接从akshare获取

    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期

    Returns:
        DataFrame: Markit制造业PMI数据
    """
    print("正在收集美国Markit制造业PMI数据...")
    print("  使用akshare获取Markit制造业PMI数据...")

    try:
        import akshare as ak

        # 使用akshare获取美国Markit制造业PMI初值报告
        manufacturing_pmi = ak.macro_usa_pmi()

        if not manufacturing_pmi.empty:
            print(f"✓ 成功从akshare获取Markit制造业PMI数据: {len(manufacturing_pmi)} 条记录")

            # 处理数据格式
            manufacturing_pmi['日期'] = pd.to_datetime(manufacturing_pmi['日期'])
            manufacturing_pmi = manufacturing_pmi.set_index('日期')
            manufacturing_pmi = manufacturing_pmi.rename(columns={'今值': 'Markit制造业PMI'})
            manufacturing_pmi['Markit制造业PMI'] = pd.to_numeric(manufacturing_pmi['Markit制造业PMI'], errors='coerce')
            manufacturing_pmi = manufacturing_pmi[['Markit制造业PMI']].dropna()

            # 按日期排序
            manufacturing_pmi = manufacturing_pmi.sort_index()

            # 筛选时间范围
            if start_date:
                manufacturing_pmi = manufacturing_pmi[manufacturing_pmi.index >= start_date]
            if end_date:
                manufacturing_pmi = manufacturing_pmi[manufacturing_pmi.index <= end_date]

            if len(manufacturing_pmi) > 0:
                print(f"  数据范围: {manufacturing_pmi.index.min().strftime('%Y-%m-%d')} 到 {manufacturing_pmi.index.max().strftime('%Y-%m-%d')}")
                print(f"  最新值: {manufacturing_pmi['Markit制造业PMI'].iloc[-1]:.1f}")

                # akshare返回的日期就是发布日期，直接使用
                manufacturing_pmi_with_release = manufacturing_pmi.copy()
                manufacturing_pmi_with_release['发布日期'] = manufacturing_pmi_with_release.index
                print(f"  已使用实际发布日期（akshare日期字段就是发布日期）")
                return manufacturing_pmi_with_release
            else:
                print("  筛选后数据为空")
        else:
            print("✗ akshare返回的Markit制造业PMI数据为空")

    except ImportError:
        print("✗ akshare未安装，无法获取Markit制造业PMI数据")
    except Exception as e:
        print(f"✗ akshare获取Markit制造业PMI数据失败: {str(e)}")

    return pd.DataFrame()

def check_akshare_availability():
    """
    检查akshare是否可用
    """
    try:
        import akshare as ak
        print("✓ akshare可用，将用于获取PMI数据")
        return True
    except ImportError:
        print("⚠️ akshare未安装，PMI数据获取可能失败")
        print("  安装命令: pip install akshare")
        return False

def combine_and_save_data(oecd_data, pce_data, services_pmi_data, manufacturing_pmi_data):
    """
    合并所有数据并保存
    """
    print("\n正在合并和保存数据...")

    # 保存各个指标的单独文件
    if not oecd_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_oecd_leading_indicator_with_release.csv')
        oecd_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ OECD领先指标数据已保存到: {output_file}")
        print(f"  包含列: {list(oecd_data.columns)}")

    if not pce_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_pce_services_with_release.csv')
        pce_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 个人消费支出服务数据已保存到: {output_file}")
        print(f"  包含列: {list(pce_data.columns)}")

    if not services_pmi_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_services_pmi_with_release.csv')
        services_pmi_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 服务业PMI数据已保存到: {output_file}")
        print(f"  包含列: {list(services_pmi_data.columns)}")

    if not manufacturing_pmi_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_manufacturing_pmi_with_release.csv')
        manufacturing_pmi_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 制造业PMI数据已保存到: {output_file}")
        print(f"  包含列: {list(manufacturing_pmi_data.columns)}")

    print(f"✓ 数据保存完成，文件位置: {DATA_DIR}")
    print("✓ 所有数据文件现在都包含发布日期信息")
    return True

def analyze_collected_data():
    """分析收集到的数据"""
    print("\n" + "=" * 60)
    print("数据分析")
    print("=" * 60)

    # 分析OECD领先指标
    try:
        oecd_file = os.path.join(DATA_DIR, 'us_oecd_leading_indicator.csv')
        if os.path.exists(oecd_file):
            oecd_df = pd.read_csv(oecd_file, index_col=0, parse_dates=True)
            print(f"\n📊 OECD领先指标分析:")
            print(f"   数据期间: {oecd_df.index.min().strftime('%Y-%m')} 至 {oecd_df.index.max().strftime('%Y-%m')}")
            print(f"   当前值: {oecd_df.iloc[-1, 0]:.2f}")
            print(f"   历史均值: {oecd_df.iloc[:, 0].mean():.2f}")
            print(f"   最近12个月变化: {((oecd_df.iloc[-1, 0] / oecd_df.iloc[-13, 0]) - 1) * 100:.2f}%")
    except Exception as e:
        print(f"   OECD数据分析失败: {e}")

    # 分析个人消费支出服务
    try:
        pce_file = os.path.join(DATA_DIR, 'us_pce_services.csv')
        if os.path.exists(pce_file):
            pce_df = pd.read_csv(pce_file, index_col=0, parse_dates=True)
            print(f"\n💰 个人消费支出服务分析:")
            print(f"   数据期间: {pce_df.index.min().strftime('%Y-%m')} 至 {pce_df.index.max().strftime('%Y-%m')}")
            print(f"   当前值: {pce_df.iloc[-1, 0]:.0f} 十亿美元")
            if len(pce_df) >= 4:
                print(f"   年度增长率: {((pce_df.iloc[-1, 0] / pce_df.iloc[-5, 0]) - 1) * 100:.2f}%")
    except Exception as e:
        print(f"   PCE数据分析失败: {e}")

    # 分析制造业PMI (akshare数据)
    try:
        mfg_file = os.path.join(DATA_DIR, 'us_manufacturing_pmi_akshare.csv')
        if os.path.exists(mfg_file):
            mfg_df = pd.read_csv(mfg_file)
            mfg_df['日期'] = pd.to_datetime(mfg_df['日期'])
            mfg_df = mfg_df.set_index('日期')
            mfg_df['今值'] = pd.to_numeric(mfg_df['今值'], errors='coerce')
            mfg_df = mfg_df.dropna(subset=['今值'])

            print(f"\n🏭 制造业PMI分析:")
            print(f"   数据期间: {mfg_df.index.min().strftime('%Y-%m')} 至 {mfg_df.index.max().strftime('%Y-%m')}")
            print(f"   当前值: {mfg_df['今值'].iloc[-1]:.1f}")
            print(f"   历史均值: {mfg_df['今值'].mean():.1f}")
            expansion_months = (mfg_df['今值'] > 50).sum()
            print(f"   扩张月份占比: {expansion_months / len(mfg_df) * 100:.1f}%")
    except Exception as e:
        print(f"   制造业PMI数据分析失败: {e}")

    # 分析服务业PMI (akshare数据)
    try:
        svc_file = os.path.join(DATA_DIR, 'us_services_pmi_akshare.csv')
        if os.path.exists(svc_file):
            svc_df = pd.read_csv(svc_file)
            svc_df['日期'] = pd.to_datetime(svc_df['日期'])
            svc_df = svc_df.set_index('日期')
            svc_df['今值'] = pd.to_numeric(svc_df['今值'], errors='coerce')
            svc_df = svc_df.dropna(subset=['今值'])

            print(f"\n🏢 服务业PMI分析:")
            print(f"   数据期间: {svc_df.index.min().strftime('%Y-%m')} 至 {svc_df.index.max().strftime('%Y-%m')}")
            print(f"   当前值: {svc_df['今值'].iloc[-1]:.1f}")
            print(f"   历史均值: {svc_df['今值'].mean():.1f}")
            expansion_months = (svc_df['今值'] > 50).sum()
            print(f"   扩张月份占比: {expansion_months / len(svc_df) * 100:.1f}%")
    except Exception as e:
        print(f"   服务业PMI数据分析失败: {e}")

def main():
    """主函数"""
    print("美国经济指标数据收集器")
    print("=" * 60)
    print("收集指标:")
    print("1. 美国OECD领先指标")
    print("2. 美国个人消费支出：服务")
    print("3. 美国服务业PMI")
    print("4. 美国制造业PMI")
    print("=" * 60)

    # 设置数据收集的时间范围
    start_date = '2000-01-01'
    end_date = datetime.now().strftime('%Y-%m-%d')

    print(f"数据收集时间范围: {start_date} 到 {end_date}")
    print("数据来源: FRED (Federal Reserve Economic Data)")
    print()

    # 收集各项指标数据
    oecd_data = collect_oecd_leading_indicator(start_date, end_date)
    time.sleep(1)  # 避免请求过于频繁

    pce_data = collect_pce_services(start_date, end_date)
    time.sleep(1)

    services_pmi_data = collect_services_pmi(start_date, end_date)
    time.sleep(1)

    manufacturing_pmi_data = collect_manufacturing_pmi(start_date, end_date)

    # 检查akshare可用性
    check_akshare_availability()

    # 保存数据
    save_result = combine_and_save_data(oecd_data, pce_data, services_pmi_data, manufacturing_pmi_data)

    # 显示数据收集总结
    print("\n" + "=" * 60)
    print("数据收集总结")
    print("=" * 60)

    success_count = 0
    if not oecd_data.empty:
        print(f"✓ OECD领先指标: {len(oecd_data)} 条记录")
        success_count += 1
    else:
        print("✗ OECD领先指标: 收集失败")

    if not pce_data.empty:
        print(f"✓ 个人消费支出服务: {len(pce_data)} 条记录")
        success_count += 1
    else:
        print("✗ 个人消费支出服务: 收集失败")

    if not services_pmi_data.empty:
        print(f"✓ 服务业PMI: {len(services_pmi_data)} 条记录")
        success_count += 1
    else:
        print("✗ 服务业PMI: 收集失败")

    if not manufacturing_pmi_data.empty:
        print(f"✓ 制造业PMI: {len(manufacturing_pmi_data)} 条记录")
        success_count += 1
    else:
        print("✗ 制造业PMI: 收集失败")

    print(f"\n成功收集 {success_count}/4 项指标")
    print(f"数据文件保存在: {DATA_DIR}")

    # 分析收集到的数据
    analyze_collected_data()

    print("\n数据收集完成！")

if __name__ == "__main__":
    main()
