#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国就业指标数据收集器

该脚本收集以下美国就业指标：
1. 失业率 (Unemployment Rate)
2. 初次申请失业金人数 (Initial Jobless Claims)
3. 非农平均时薪 (Average Hourly Earnings)
4. 平均每周工时 (Average Weekly Hours)
5. 新增ADP就业人数 (ADP Employment Change)

数据来源:
- FRED (Federal Reserve Economic Data) (主要数据源，包含发布日期信息)
- akshare (备用数据源)

重要说明：
美国就业数据的发布时间与数据实际对应时间存在偏移：
- 失业率：通常在下个月第一个周五发布上个月数据
- 初次申请失业金人数：每周四发布上周数据
- 非农平均时薪：与非农就业报告同时发布，滞后1个月
- 平均每周工时：与非农就业报告同时发布，滞后1个月
- ADP就业数据：通常在月初发布上个月数据

本脚本会自动调整时间偏移，确保数据日期对应实际统计期间。
同时保留原始发布日期信息，方便用户了解数据的实际发布时间。

数据字段说明：
- 数据日期（索引）：数据实际对应的统计期间
- 发布日期：数据的实际发布时间
- 各项指标值：具体的经济指标数值
"""

import pandas as pd
import pandas_datareader.data as pdr
import os
from datetime import datetime
import warnings
import time
import requests
from dateutil.relativedelta import relativedelta

# 忽略警告信息
warnings.filterwarnings('ignore')

# FRED API配置
FRED_API_KEY = "c45462cf6c06e45d52d75b9d31b7a07f"
FRED_BASE_URL = "https://api.stlouisfed.org/fred"

# FRED API发布日期缓存
_release_dates_cache = {}

# 设置数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

def get_series_release_info(series_id, max_retries=3):
    """从FRED API获取数据系列的发布信息"""
    url = f"{FRED_BASE_URL}/series/release"
    params = {
        'series_id': series_id,
        'api_key': FRED_API_KEY,
        'file_type': 'json'
    }

    for attempt in range(max_retries):
        try:
            print(f"    尝试获取 {series_id} 发布信息 (第{attempt+1}次)...")
            response = requests.get(url, params=params, timeout=15, verify=False)
            if response.status_code == 200:
                data = response.json()
                if 'releases' in data and len(data['releases']) > 0:
                    release_info = data['releases'][0]
                    print(f"    ✓ 成功获取发布信息: {release_info.get('name', 'Unknown')}")
                    return release_info
            else:
                print(f"    ❌ API返回状态码: {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"    ⏰ 请求超时 (第{attempt+1}次)")
        except requests.exceptions.SSLError:
            print(f"    🔒 SSL错误 (第{attempt+1}次)")
        except Exception as e:
            print(f"    ❌ 请求失败: {str(e)}")

        if attempt < max_retries - 1:
            print(f"    等待2秒后重试...")
            time.sleep(2)

    print(f"    ❌ 获取 {series_id} 发布信息失败，已重试{max_retries}次")
    return None

def get_release_dates(release_id, max_retries=3):
    """从FRED API获取特定发布的所有发布日期"""
    # 检查缓存
    if release_id in _release_dates_cache:
        print(f"    ✓ 使用缓存的发布日期数据")
        return _release_dates_cache[release_id]

    url = f"{FRED_BASE_URL}/release/dates"
    params = {
        'release_id': release_id,
        'api_key': FRED_API_KEY,
        'file_type': 'json',
        'sort_order': 'asc',
        'limit': 1000,
        'include_release_dates_with_no_data': 'true'
    }

    for attempt in range(max_retries):
        try:
            print(f"    尝试获取发布日期 (第{attempt+1}次)...")
            response = requests.get(url, params=params, timeout=15, verify=False)
            if response.status_code == 200:
                data = response.json()
                if 'release_dates' in data:
                    # 转换为日期映射
                    release_dates = {}
                    for date_info in data['release_dates']:
                        date_str = date_info['date']
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        release_dates[date_str] = date_obj

                    print(f"    ✓ 成功获取 {len(release_dates)} 个发布日期")
                    # 缓存结果
                    _release_dates_cache[release_id] = release_dates
                    return release_dates
            else:
                print(f"    ❌ API返回状态码: {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"    ⏰ 请求超时 (第{attempt+1}次)")
        except requests.exceptions.SSLError:
            print(f"    🔒 SSL错误 (第{attempt+1}次)")
        except Exception as e:
            print(f"    ❌ 请求失败: {str(e)}")

        if attempt < max_retries - 1:
            print(f"    等待2秒后重试...")
            time.sleep(2)

    print(f"    ❌ 获取发布日期失败，已重试{max_retries}次")
    return {}

def calculate_release_date_fallback(data_date, indicator_name):
    """
    备用的发布日期计算方法（基于规律计算）
    当FRED API不可用时使用
    """
    if '失业率' in indicator_name or 'unemployment' in indicator_name.lower():
        # 失业率：通常在下个月第一个周五发布上个月数据
        next_month = data_date + relativedelta(months=1)
        first_day = next_month.replace(day=1)
        # 找到第一个周五
        days_until_friday = (4 - first_day.weekday()) % 7
        if days_until_friday == 0:
            days_until_friday = 7
        first_friday = first_day + relativedelta(days=days_until_friday)
        return first_friday

    elif '初次申请失业金' in indicator_name or 'jobless claims' in indicator_name.lower():
        # 初次申请失业金人数：每周四发布上周数据
        # 数据日期通常是周六，发布日期是下周四
        days_until_thursday = (3 - data_date.weekday()) % 7
        if days_until_thursday == 0:
            days_until_thursday = 7
        return data_date + relativedelta(days=days_until_thursday)

    elif '平均时薪' in indicator_name or 'hourly earnings' in indicator_name.lower():
        # 非农平均时薪：与非农就业报告同时发布，下个月第一个周五
        next_month = data_date + relativedelta(months=1)
        first_day = next_month.replace(day=1)
        days_until_friday = (4 - first_day.weekday()) % 7
        if days_until_friday == 0:
            days_until_friday = 7
        first_friday = first_day + relativedelta(days=days_until_friday)
        return first_friday

    elif '每周工时' in indicator_name or 'weekly hours' in indicator_name.lower():
        # 平均每周工时：与非农就业报告同时发布，下个月第一个周五
        next_month = data_date + relativedelta(months=1)
        first_day = next_month.replace(day=1)
        days_until_friday = (4 - first_day.weekday()) % 7
        if days_until_friday == 0:
            days_until_friday = 7
        first_friday = first_day + relativedelta(days=days_until_friday)
        return first_friday

    elif 'ADP' in indicator_name:
        # ADP就业数据：通常在月初发布上个月数据
        next_month = data_date + relativedelta(months=1)
        # 通常在下个月2-5号之间发布
        return next_month.replace(day=3)

    else:
        # 默认：下个月15号
        next_month = data_date + relativedelta(months=1)
        return next_month.replace(day=15)

def find_closest_release_date(data_date, release_dates, indicator_name):
    """
    在FRED API的发布日期中找到最接近的发布日期
    """
    # 对于就业类指标，寻找数据日期之后的合理发布日期
    if '失业率' in indicator_name or '平均时薪' in indicator_name or '每周工时' in indicator_name:
        # 失业率、平均时薪、每周工时通常在下个月第一个周五发布
        next_month = data_date + relativedelta(months=1)
        target_start = next_month.replace(day=1)   # 下个月1号开始寻找
        target_end = next_month.replace(day=15)    # 下个月15号结束寻找

        closest_date = None
        min_diff = float('inf')

        for date_str, date_obj in release_dates.items():
            if target_start <= date_obj <= target_end:
                # 优先选择周五
                if date_obj.weekday() == 4:  # 周五
                    expected_date = next_month.replace(day=7)  # 预期在下个月第一个周五左右
                    diff = abs((date_obj - expected_date).days)
                    if diff < min_diff:
                        min_diff = diff
                        closest_date = date_obj

        return closest_date

    elif '初次申请失业金' in indicator_name:
        # 初次申请失业金人数通常每周四发布
        # 寻找数据日期之后的下一个周四
        target_start = data_date
        target_end = data_date + relativedelta(days=14)  # 两周内

        closest_date = None
        min_diff = float('inf')

        for date_str, date_obj in release_dates.items():
            if target_start <= date_obj <= target_end:
                # 优先选择周四
                if date_obj.weekday() == 3:  # 周四
                    diff = abs((date_obj - data_date).days)
                    if diff < min_diff:
                        min_diff = diff
                        closest_date = date_obj

        return closest_date

    elif 'ADP' in indicator_name:
        # ADP数据通常在月初发布
        next_month = data_date + relativedelta(months=1)
        target_start = next_month.replace(day=1)
        target_end = next_month.replace(day=10)

        closest_date = None
        min_diff = float('inf')

        for date_str, date_obj in release_dates.items():
            if target_start <= date_obj <= target_end:
                diff = abs((date_obj - next_month.replace(day=3)).days)
                if diff < min_diff:
                    min_diff = diff
                    closest_date = date_obj

        return closest_date

    # 如果没找到精确匹配，返回None使用备用方法
    return None

def add_release_dates(df, indicator_name, series_id=None):
    """
    为DataFrame添加发布日期列

    Args:
        df (DataFrame): 原始数据
        indicator_name (str): 指标名称
        series_id (str): FRED系列ID（可选，用于API获取）

    Returns:
        DataFrame: 添加发布日期后的数据
    """
    if df.empty:
        return df

    print(f"  正在添加发布日期信息...")

    # 如果有series_id，先获取一次发布信息和发布日期
    fred_release_dates = {}
    if series_id:
        print(f"  使用FRED API获取 {series_id} 的发布日期...")
        release_info = get_series_release_info(series_id)
        if release_info:
            release_id = release_info['id']
            fred_release_dates = get_release_dates(release_id)
            print(f"  ✓ 获取到 {len(fred_release_dates)} 个FRED发布日期")
        else:
            print(f"  ❌ 无法获取FRED发布信息，将使用计算方法")

    # 计算发布日期
    release_dates = []
    api_success_count = 0
    fallback_count = 0

    for date_index in df.index:
        if fred_release_dates:
            # 尝试从FRED API数据中匹配
            matched_date = find_closest_release_date(date_index, fred_release_dates, indicator_name)
            if matched_date:
                release_dates.append(matched_date)
                api_success_count += 1
            else:
                # 使用备用计算方法
                fallback_date = calculate_release_date_fallback(date_index, indicator_name)
                release_dates.append(fallback_date)
                fallback_count += 1
        else:
            # 直接使用计算方法
            release_date = calculate_release_date_fallback(date_index, indicator_name)
            release_dates.append(release_date)
            fallback_count += 1

    # 添加发布日期列
    df_with_release = df.copy()
    df_with_release['发布日期'] = release_dates

    # 重新排列列顺序，将发布日期放在第二列
    cols = df_with_release.columns.tolist()
    if '发布日期' in cols:
        cols.remove('发布日期')
        cols.insert(1, '发布日期')
        df_with_release = df_with_release[cols]

    # 显示统计信息
    if series_id and fred_release_dates:
        total_count = len(release_dates)
        print(f"  发布日期获取统计: API成功 {api_success_count}/{total_count}, 备用方法 {fallback_count}/{total_count}")
    else:
        print(f"  使用计算方法添加了 {len(release_dates)} 个发布日期")

    return df_with_release

def collect_unemployment_rate(start_date=None, end_date=None):
    """
    收集美国失业率数据

    Args:
        start_date (str): 开始日期，格式为'YYYY-MM-DD'
        end_date (str): 结束日期，格式为'YYYY-MM-DD'

    Returns:
        DataFrame: 失业率数据
    """
    print("正在收集美国失业率数据...")

    # 首先尝试使用FRED（优先数据源）
    print("  尝试使用FRED数据源...")
    try:
        # FRED series ID for Unemployment Rate
        series_id = 'UNRATE'  # Unemployment Rate

        # 从FRED获取数据
        df = pdr.get_data_fred(series_id, start=start_date, end=end_date)

        # 重命名列
        df.columns = ['失业率']

        # 计算变化
        df['失业率_变化'] = df['失业率'].diff()

        # 移除缺失值
        df = df.dropna()

        if len(df) > 0:
            print(f"✓ 成功从FRED获取失业率数据: {len(df)} 条记录")
            print(f"  数据范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  最新值: {df['失业率'].iloc[-1]:.1f}%")

            # 添加发布日期（使用FRED API）
            df_with_release = add_release_dates(df, '失业率', series_id)
            return df_with_release

        return df

    except Exception as e:
        print(f"✗ FRED获取失业率数据失败: {str(e)}")

    # 如果FRED失败，回退到akshare
    print("  回退到akshare数据源...")
    try:
        import akshare as ak
        print("  尝试使用akshare数据源...")

        # 获取失业率数据
        unemployment_data = ak.macro_usa_unemployment_rate()

        if not unemployment_data.empty:
            print(f"✓ 成功从akshare获取失业率数据: {len(unemployment_data)} 条记录")

            # 处理数据格式
            unemployment_data['日期'] = pd.to_datetime(unemployment_data['日期'])

            # 重要：将发布日期调整为数据实际对应的月份（向前偏移1个月）
            # 因为失业率数据是在下个月发布上个月的数据
            unemployment_data['发布日期'] = unemployment_data['日期']  # 保留原始发布日期
            unemployment_data['数据日期'] = unemployment_data['日期'] - pd.DateOffset(months=1)
            unemployment_data['数据日期'] = unemployment_data['数据日期'].dt.to_period('M').dt.to_timestamp()

            unemployment_data = unemployment_data.set_index('数据日期')
            unemployment_data = unemployment_data.rename(columns={'今值': '失业率'})
            unemployment_data['失业率'] = pd.to_numeric(unemployment_data['失业率'], errors='coerce')
            unemployment_data = unemployment_data[['失业率', '发布日期']].dropna()

            # 计算变化
            unemployment_data['失业率_变化'] = unemployment_data['失业率'].diff()

            # 按日期排序
            unemployment_data = unemployment_data.sort_index()

            print(f"  ✓ 已调整时间偏移：发布日期 -> 数据实际月份")

            # 筛选时间范围
            if start_date:
                unemployment_data = unemployment_data[unemployment_data.index >= start_date]
            if end_date:
                unemployment_data = unemployment_data[unemployment_data.index <= end_date]

            if len(unemployment_data) > 0:
                print(f"  数据范围: {unemployment_data.index.min().strftime('%Y-%m-%d')} 到 {unemployment_data.index.max().strftime('%Y-%m-%d')}")
                print(f"  最新值: {unemployment_data['失业率'].iloc[-1]:.1f}%")
                return unemployment_data
            else:
                print("  筛选后数据为空")
        else:
            print("✗ akshare返回的失业率数据为空")

    except ImportError:
        print("✗ akshare未安装，无法获取失业率数据")
    except Exception as e:
        print(f"✗ akshare获取失业率数据失败: {str(e)}")

    return pd.DataFrame()

def collect_initial_jobless_claims(start_date=None, end_date=None):
    """
    收集初次申请失业金人数数据

    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期

    Returns:
        DataFrame: 初次申请失业金人数数据
    """
    print("正在收集初次申请失业金人数数据...")

    # 首先尝试使用FRED（优先数据源）
    print("  尝试使用FRED数据源...")
    try:
        # FRED series ID for Initial Claims
        series_id = 'ICSA'  # Initial Claims

        # 从FRED获取数据
        df = pdr.get_data_fred(series_id, start=start_date, end=end_date)

        # 重命名列
        df.columns = ['初次申请失业金人数']

        # 计算变化和移动平均
        df['初次申请失业金人数_变化'] = df['初次申请失业金人数'].diff()
        df['初次申请失业金人数_4周均值'] = df['初次申请失业金人数'].rolling(4).mean()

        # 移除缺失值
        df = df.dropna()

        if len(df) > 0:
            print(f"✓ 成功从FRED获取初次申请失业金人数数据: {len(df)} 条记录")
            print(f"  数据范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  最新值: {df['初次申请失业金人数'].iloc[-1]:,.0f}人")

            # 添加发布日期（使用FRED API）
            df_with_release = add_release_dates(df, '初次申请失业金人数', series_id)
            return df_with_release

        return df

    except Exception as e:
        print(f"✗ FRED获取初次申请失业金人数数据失败: {str(e)}")

    # 如果FRED失败，回退到akshare
    print("  回退到akshare数据源...")
    try:
        import akshare as ak
        print("  尝试使用akshare数据源...")

        # 获取初次申请失业金人数数据
        jobless_claims_data = ak.macro_usa_initial_jobless()

        if not jobless_claims_data.empty:
            print(f"✓ 成功从akshare获取初次申请失业金人数数据: {len(jobless_claims_data)} 条记录")

            # 处理数据格式
            jobless_claims_data['日期'] = pd.to_datetime(jobless_claims_data['日期'])

            # 重要：初次申请失业金人数通常是周度数据，发布时间相对及时
            # 但为了保持一致性，我们也进行适当的时间调整
            # 将数据日期设置为当周的周四（通常是数据统计截止日）
            jobless_claims_data['发布日期'] = jobless_claims_data['日期']  # 保留原始发布日期
            jobless_claims_data['数据日期'] = jobless_claims_data['日期'] - pd.Timedelta(days=3)

            jobless_claims_data = jobless_claims_data.set_index('数据日期')
            jobless_claims_data = jobless_claims_data.rename(columns={'今值': '初次申请失业金人数'})
            jobless_claims_data['初次申请失业金人数'] = pd.to_numeric(jobless_claims_data['初次申请失业金人数'], errors='coerce')
            jobless_claims_data = jobless_claims_data[['初次申请失业金人数', '发布日期']].dropna()

            # 计算变化和移动平均
            jobless_claims_data['初次申请失业金人数_变化'] = jobless_claims_data['初次申请失业金人数'].diff()
            jobless_claims_data['初次申请失业金人数_4周均值'] = jobless_claims_data['初次申请失业金人数'].rolling(4).mean()

            # 按日期排序
            jobless_claims_data = jobless_claims_data.sort_index()

            print(f"  ✓ 已调整时间偏移：发布日期 -> 数据统计截止日")

            # 筛选时间范围
            if start_date:
                jobless_claims_data = jobless_claims_data[jobless_claims_data.index >= start_date]
            if end_date:
                jobless_claims_data = jobless_claims_data[jobless_claims_data.index <= end_date]

            if len(jobless_claims_data) > 0:
                print(f"  数据范围: {jobless_claims_data.index.min().strftime('%Y-%m-%d')} 到 {jobless_claims_data.index.max().strftime('%Y-%m-%d')}")
                print(f"  最新值: {jobless_claims_data['初次申请失业金人数'].iloc[-1]:,.0f}人")
                return jobless_claims_data
            else:
                print("  筛选后数据为空")
        else:
            print("✗ akshare返回的初次申请失业金人数数据为空")

    except ImportError:
        print("✗ akshare未安装，无法获取初次申请失业金人数数据")
    except Exception as e:
        print(f"✗ akshare获取初次申请失业金人数数据失败: {str(e)}")

    return pd.DataFrame()

def collect_average_hourly_earnings(start_date=None, end_date=None):
    """
    收集非农平均时薪数据
    
    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期
    
    Returns:
        DataFrame: 非农平均时薪数据
    """
    print("正在收集非农平均时薪数据...")
    
    # 使用FRED数据源
    print("  使用FRED数据源...")
    try:
        # FRED series ID for Average Hourly Earnings
        series_id = 'AHETPI'  # Average Hourly Earnings of Production and Nonsupervisory Employees, Total Private
        
        # 从FRED获取数据
        df = pdr.get_data_fred(series_id, start=start_date, end=end_date)
        
        # 重命名列
        df.columns = ['非农平均时薪']

        # 计算变化率
        df['非农平均时薪_月率'] = df['非农平均时薪'].pct_change() * 100
        df['非农平均时薪_年率'] = df['非农平均时薪'].pct_change(12) * 100

        # 移除缺失值
        df = df.dropna()

        if len(df) > 0:
            print(f"✓ 成功从FRED获取非农平均时薪数据: {len(df)} 条记录")
            print(f"  数据范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  最新值: ${df['非农平均时薪'].iloc[-1]:.2f}")
            print(f"  最新年率: {df['非农平均时薪_年率'].iloc[-1]:.2f}%")

            # 添加发布日期（使用FRED API）
            df_with_release = add_release_dates(df, '非农平均时薪', series_id)
            return df_with_release

        return df
        
    except Exception as e:
        print(f"✗ FRED获取非农平均时薪数据失败: {str(e)}")
        return pd.DataFrame()

def collect_average_weekly_hours(start_date=None, end_date=None):
    """
    收集平均每周工时数据
    
    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期
    
    Returns:
        DataFrame: 平均每周工时数据
    """
    print("正在收集平均每周工时数据...")
    
    # 使用FRED数据源
    print("  使用FRED数据源...")
    try:
        # FRED series ID for Average Weekly Hours
        series_id = 'AWHAETP'  # Average Weekly Hours of Production and Nonsupervisory Employees, Total Private
        
        # 从FRED获取数据
        df = pdr.get_data_fred(series_id, start=start_date, end=end_date)
        
        # 重命名列
        df.columns = ['平均每周工时']

        # 计算变化
        df['平均每周工时_变化'] = df['平均每周工时'].diff()
        df['平均每周工时_年率'] = df['平均每周工时'].pct_change(12) * 100

        # 移除缺失值
        df = df.dropna()

        if len(df) > 0:
            print(f"✓ 成功从FRED获取平均每周工时数据: {len(df)} 条记录")
            print(f"  数据范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
            print(f"  最新值: {df['平均每周工时'].iloc[-1]:.1f}小时")
            print(f"  最新变化: {df['平均每周工时_变化'].iloc[-1]:.2f}小时")

            # 添加发布日期（使用FRED API）
            df_with_release = add_release_dates(df, '平均每周工时', series_id)
            return df_with_release

        return df

    except Exception as e:
        print(f"✗ FRED获取平均每周工时数据失败: {str(e)}")
        return pd.DataFrame()

def collect_adp_employment_change(start_date=None, end_date=None):
    """
    收集新增ADP就业人数数据

    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期

    Returns:
        DataFrame: 新增ADP就业人数数据
    """
    print("正在收集新增ADP就业人数数据...")

    # 首先尝试使用FRED（虽然ADP数据通常不在FRED中）
    print("  尝试使用FRED数据源...")
    try:
        # 注意：ADP数据通常不在FRED中，这里作为示例
        print("  ⚠️  ADP数据通常不在FRED中，跳过FRED数据源")
        raise Exception("ADP数据不在FRED中")

    except Exception as e:
        print(f"  FRED不包含ADP数据，转向akshare")

    # 回退到akshare
    print("  使用akshare数据源...")
    try:
        import akshare as ak
        print("  尝试使用akshare数据源...")

        # 获取ADP就业人数数据
        adp_data = ak.macro_usa_adp_employment()

        if not adp_data.empty:
            print(f"✓ 成功从akshare获取ADP就业人数数据: {len(adp_data)} 条记录")

            # 处理数据格式
            adp_data['日期'] = pd.to_datetime(adp_data['日期'])

            # 重要：ADP就业数据发布规律：每月第一个周三发布上个月的数据
            # akshare的'日期'字段实际上是数据所属的月份，不是发布日期

            def calculate_adp_release_date(data_month):
                """
                计算ADP数据的实际发布日期
                ADP数据在每月第一个周三发布上个月的数据
                """
                # 数据月份的下个月（发布月份）
                release_month = data_month + pd.DateOffset(months=1)
                # 找到该月的第一个周三
                first_day = release_month.replace(day=1)
                # 周三是weekday=2
                days_until_wednesday = (2 - first_day.weekday()) % 7
                if days_until_wednesday == 0 and first_day.weekday() != 2:
                    days_until_wednesday = 7
                first_wednesday = first_day + pd.Timedelta(days=days_until_wednesday)
                return first_wednesday

            # 将akshare的日期作为数据所属月份
            adp_data['数据日期'] = adp_data['日期'].dt.to_period('M').dt.to_timestamp()
            # 计算真实的发布日期（下个月第一个周三）
            adp_data['发布日期'] = adp_data['数据日期'].apply(calculate_adp_release_date)

            adp_data = adp_data.set_index('数据日期')
            adp_data = adp_data.rename(columns={'今值': '新增ADP就业人数'})
            adp_data['新增ADP就业人数'] = pd.to_numeric(adp_data['新增ADP就业人数'], errors='coerce')
            adp_data = adp_data[['新增ADP就业人数', '发布日期']].dropna()

            # 计算移动平均
            adp_data['新增ADP就业人数_3月均值'] = adp_data['新增ADP就业人数'].rolling(3).mean()
            adp_data['新增ADP就业人数_6月均值'] = adp_data['新增ADP就业人数'].rolling(6).mean()

            # 按日期排序
            adp_data = adp_data.sort_index()

            print(f"  ✓ 已调整时间偏移：发布日期 -> 数据实际月份")

            # 筛选时间范围
            if start_date:
                adp_data = adp_data[adp_data.index >= start_date]
            if end_date:
                adp_data = adp_data[adp_data.index <= end_date]

            if len(adp_data) > 0:
                print(f"  数据范围: {adp_data.index.min().strftime('%Y-%m-%d')} 到 {adp_data.index.max().strftime('%Y-%m-%d')}")
                print(f"  最新值: {adp_data['新增ADP就业人数'].iloc[-1]:,.0f}人")
                return adp_data
            else:
                print("  筛选后数据为空")
        else:
            print("✗ akshare返回的ADP就业人数数据为空")

    except ImportError:
        print("✗ akshare未安装，无法获取ADP就业人数数据")
    except Exception as e:
        print(f"✗ akshare获取ADP就业人数数据失败: {str(e)}")

    return pd.DataFrame()

def check_akshare_availability():
    """
    检查akshare是否可用
    """
    try:
        __import__('akshare')
        print("✓ akshare可用，将用于获取就业数据")
        return True
    except ImportError:
        print("⚠️ akshare未安装，部分数据获取可能失败")
        print("  安装命令: pip install akshare")
        return False

def combine_and_save_data(unemployment_data, jobless_claims_data, hourly_earnings_data, weekly_hours_data, adp_data):
    """
    合并所有数据并保存
    """
    print("\n正在合并和保存数据...")

    # 保存各个指标的单独文件
    if not unemployment_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_unemployment_rate.csv')
        unemployment_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 失业率数据已保存到: {output_file}")

    if not jobless_claims_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_initial_jobless_claims.csv')
        jobless_claims_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 初次申请失业金人数数据已保存到: {output_file}")

    if not hourly_earnings_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_average_hourly_earnings.csv')
        hourly_earnings_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 非农平均时薪数据已保存到: {output_file}")

    if not weekly_hours_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_average_weekly_hours.csv')
        weekly_hours_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 平均每周工时数据已保存到: {output_file}")

    if not adp_data.empty:
        output_file = os.path.join(DATA_DIR, 'us_adp_employment_change.csv')
        adp_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 新增ADP就业人数数据已保存到: {output_file}")

    print(f"✓ 数据保存完成，文件位置: {DATA_DIR}")
    return True

def analyze_collected_data():
    """分析收集到的数据"""
    print("\n" + "=" * 60)
    print("就业数据分析")
    print("=" * 60)

    # 分析失业率
    try:
        unemployment_file = os.path.join(DATA_DIR, 'us_unemployment_rate.csv')
        if os.path.exists(unemployment_file):
            unemployment_df = pd.read_csv(unemployment_file, index_col=0, parse_dates=True)
            print(f"\n📊 失业率分析:")
            print(f"   数据期间: {unemployment_df.index.min().strftime('%Y-%m')} 至 {unemployment_df.index.max().strftime('%Y-%m')}")
            print(f"   当前失业率: {unemployment_df['失业率'].iloc[-1]:.1f}%")
            print(f"   历史平均: {unemployment_df['失业率'].mean():.1f}%")
            print(f"   最近12个月平均: {unemployment_df['失业率'].iloc[-12:].mean():.1f}%")
            if '发布日期' in unemployment_df.columns:
                latest_release = pd.to_datetime(unemployment_df['发布日期'].iloc[-1])
                print(f"   最新数据发布时间: {latest_release.strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"   失业率数据分析失败: {e}")

    # 分析初次申请失业金人数
    try:
        jobless_claims_file = os.path.join(DATA_DIR, 'us_initial_jobless_claims.csv')
        if os.path.exists(jobless_claims_file):
            jobless_claims_df = pd.read_csv(jobless_claims_file, index_col=0, parse_dates=True)
            print(f"\n💼 初次申请失业金人数分析:")
            print(f"   数据期间: {jobless_claims_df.index.min().strftime('%Y-%m')} 至 {jobless_claims_df.index.max().strftime('%Y-%m')}")
            print(f"   当前值: {jobless_claims_df['初次申请失业金人数'].iloc[-1]:,.0f}人")
            print(f"   历史平均: {jobless_claims_df['初次申请失业金人数'].mean():,.0f}人")
            if '初次申请失业金人数_4周均值' in jobless_claims_df.columns:
                print(f"   4周移动平均: {jobless_claims_df['初次申请失业金人数_4周均值'].iloc[-1]:,.0f}人")
            if '发布日期' in jobless_claims_df.columns:
                latest_release = pd.to_datetime(jobless_claims_df['发布日期'].iloc[-1])
                print(f"   最新数据发布时间: {latest_release.strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"   初次申请失业金人数数据分析失败: {e}")

    # 分析非农平均时薪
    try:
        hourly_earnings_file = os.path.join(DATA_DIR, 'us_average_hourly_earnings.csv')
        if os.path.exists(hourly_earnings_file):
            hourly_earnings_df = pd.read_csv(hourly_earnings_file, index_col=0, parse_dates=True)
            print(f"\n💰 非农平均时薪分析:")
            print(f"   数据期间: {hourly_earnings_df.index.min().strftime('%Y-%m')} 至 {hourly_earnings_df.index.max().strftime('%Y-%m')}")
            print(f"   当前时薪: ${hourly_earnings_df['非农平均时薪'].iloc[-1]:.2f}")
            if '非农平均时薪_年率' in hourly_earnings_df.columns:
                print(f"   年增长率: {hourly_earnings_df['非农平均时薪_年率'].iloc[-1]:.2f}%")
                print(f"   历史年增长率均值: {hourly_earnings_df['非农平均时薪_年率'].mean():.2f}%")
            if '发布日期' in hourly_earnings_df.columns:
                latest_release = pd.to_datetime(hourly_earnings_df['发布日期'].iloc[-1])
                print(f"   最新数据发布时间: {latest_release.strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"   非农平均时薪数据分析失败: {e}")

    # 分析平均每周工时
    try:
        weekly_hours_file = os.path.join(DATA_DIR, 'us_average_weekly_hours.csv')
        if os.path.exists(weekly_hours_file):
            weekly_hours_df = pd.read_csv(weekly_hours_file, index_col=0, parse_dates=True)
            print(f"\n⏰ 平均每周工时分析:")
            print(f"   数据期间: {weekly_hours_df.index.min().strftime('%Y-%m')} 至 {weekly_hours_df.index.max().strftime('%Y-%m')}")
            print(f"   当前工时: {weekly_hours_df['平均每周工时'].iloc[-1]:.1f}小时")
            print(f"   历史平均: {weekly_hours_df['平均每周工时'].mean():.1f}小时")
            if '平均每周工时_变化' in weekly_hours_df.columns:
                print(f"   最新变化: {weekly_hours_df['平均每周工时_变化'].iloc[-1]:.2f}小时")
            if '发布日期' in weekly_hours_df.columns:
                latest_release = pd.to_datetime(weekly_hours_df['发布日期'].iloc[-1])
                print(f"   最新数据发布时间: {latest_release.strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"   平均每周工时数据分析失败: {e}")

    # 分析新增ADP就业人数
    try:
        adp_file = os.path.join(DATA_DIR, 'us_adp_employment_change.csv')
        if os.path.exists(adp_file):
            adp_df = pd.read_csv(adp_file, index_col=0, parse_dates=True)
            print(f"\n🏢 新增ADP就业人数分析:")
            print(f"   数据期间: {adp_df.index.min().strftime('%Y-%m')} 至 {adp_df.index.max().strftime('%Y-%m')}")
            print(f"   当前值: {adp_df['新增ADP就业人数'].iloc[-1]:,.0f}人")
            print(f"   历史平均: {adp_df['新增ADP就业人数'].mean():,.0f}人")
            if '新增ADP就业人数_3月均值' in adp_df.columns:
                print(f"   3月移动平均: {adp_df['新增ADP就业人数_3月均值'].iloc[-1]:,.0f}人")
            if '发布日期' in adp_df.columns:
                latest_release = pd.to_datetime(adp_df['发布日期'].iloc[-1])
                print(f"   最新数据发布时间: {latest_release.strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"   新增ADP就业人数数据分析失败: {e}")

def main():
    """主函数"""
    print("美国就业指标数据收集器")
    print("=" * 60)
    print("收集指标:")
    print("1. 失业率 (Unemployment Rate)")
    print("2. 初次申请失业金人数 (Initial Jobless Claims)")
    print("3. 非农平均时薪 (Average Hourly Earnings)")
    print("4. 平均每周工时 (Average Weekly Hours)")
    print("5. 新增ADP就业人数 (ADP Employment Change)")
    print("=" * 60)

    # 设置数据收集的时间范围
    start_date = '2000-01-01'
    end_date = datetime.now().strftime('%Y-%m-%d')

    print(f"数据收集时间范围: {start_date} 到 {end_date}")
    print("数据来源: FRED (主要，含发布日期) + akshare (备用)")
    print()

    # 检查akshare可用性
    check_akshare_availability()
    print()

    # 收集各项指标数据
    unemployment_data = collect_unemployment_rate(start_date, end_date)
    time.sleep(1)  # 避免请求过于频繁

    jobless_claims_data = collect_initial_jobless_claims(start_date, end_date)
    time.sleep(1)

    hourly_earnings_data = collect_average_hourly_earnings(start_date, end_date)
    time.sleep(1)

    weekly_hours_data = collect_average_weekly_hours(start_date, end_date)
    time.sleep(1)

    adp_data = collect_adp_employment_change(start_date, end_date)

    # 保存数据
    combine_and_save_data(unemployment_data, jobless_claims_data, hourly_earnings_data, weekly_hours_data, adp_data)

    # 显示数据收集总结
    print("\n" + "=" * 60)
    print("数据收集总结")
    print("=" * 60)

    success_count = 0
    if not unemployment_data.empty:
        print(f"✓ 失业率: {len(unemployment_data)} 条记录")
        success_count += 1
    else:
        print("✗ 失业率: 收集失败")

    if not jobless_claims_data.empty:
        print(f"✓ 初次申请失业金人数: {len(jobless_claims_data)} 条记录")
        success_count += 1
    else:
        print("✗ 初次申请失业金人数: 收集失败")

    if not hourly_earnings_data.empty:
        print(f"✓ 非农平均时薪: {len(hourly_earnings_data)} 条记录")
        success_count += 1
    else:
        print("✗ 非农平均时薪: 收集失败")

    if not weekly_hours_data.empty:
        print(f"✓ 平均每周工时: {len(weekly_hours_data)} 条记录")
        success_count += 1
    else:
        print("✗ 平均每周工时: 收集失败")

    if not adp_data.empty:
        print(f"✓ 新增ADP就业人数: {len(adp_data)} 条记录")
        success_count += 1
    else:
        print("✗ 新增ADP就业人数: 收集失败")

    print(f"\n成功收集 {success_count}/5 项指标")
    print(f"数据文件保存在: {DATA_DIR}")

    # 分析收集到的数据
    analyze_collected_data()

    print("\n数据收集完成！")

if __name__ == "__main__":
    main()
