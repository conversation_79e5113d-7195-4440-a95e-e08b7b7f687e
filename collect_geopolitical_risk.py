#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地缘政治风险指数(日频)数据收集器

该脚本专门收集日频地缘政治风险指数数据：
- 地缘政治风险指数 (Geopolitical Risk Index, GPR) - 日频数据

数据来源:
- 直接从Matteo Iacoviello官方网站获取日频数据

重要说明：
地缘政治风险指数由Caldara和Iacoviello开发，基于新闻文本分析：
- GPR指数：综合地缘政治风险水平，基于新闻报道中的地缘政治关键词
- 指数上升表示地缘政治风险增加，可能影响金融市场和经济
- 100为基准值，高于100表示风险高于历史平均水平
- 本脚本专门获取日频数据，提供更高时间分辨率的风险监控
"""

import pandas as pd
import os
import warnings
import requests

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

def collect_daily_gpr_data():
    """
    收集日频地缘政治风险指数数据

    Returns:
        DataFrame: 日频地缘政治风险指数数据
    """
    print("正在收集日频地缘政治风险指数数据...")

    # 日频GPR数据的官方URL
    url = "https://www.matteoiacoviello.com/gpr_files/data_gpr_daily_recent.xls"

    try:
        print(f"  从官方源下载日频数据: {url}")

        # 下载数据
        response = requests.get(url, timeout=30)

        if response.status_code == 200:
            # 保存临时文件
            temp_file = os.path.join(DATA_DIR, 'temp_gpr_daily.xls')
            with open(temp_file, 'wb') as f:
                f.write(response.content)

            # 读取Excel文件
            try:
                # 首先查看有哪些sheet
                excel_file = pd.ExcelFile(temp_file)
                print(f"    Excel文件包含的sheet: {excel_file.sheet_names}")

                # 尝试读取不同的sheet来找到日频数据
                for sheet_name in excel_file.sheet_names:
                    try:
                        df = pd.read_excel(temp_file, sheet_name=sheet_name)
                        print(f"    Sheet '{sheet_name}': {len(df)} 行, {len(df.columns)} 列")

                        if len(df) > 0:
                            print(f"    列名: {list(df.columns)[:10]}")

                            # 查找日期列
                            date_cols = [col for col in df.columns if 'date' in col.lower() or 'time' in col.lower()]
                            if date_cols:
                                print(f"    找到日期列: {date_cols}")

                                # 处理数据格式
                                date_col = date_cols[0]
                                df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
                                df = df.dropna(subset=[date_col])
                                df = df.set_index(date_col)

                                # 查找GPR相关列
                                gpr_cols = [col for col in df.columns if 'gpr' in col.lower() or 'risk' in col.lower()]
                                if gpr_cols:
                                    print(f"    找到GPR列: {gpr_cols}")

                                    # 选择主要的GPR列
                                    main_gpr_col = gpr_cols[0]
                                    df_clean = pd.DataFrame()
                                    df_clean['地缘政治风险指数'] = pd.to_numeric(df[main_gpr_col], errors='coerce')
                                    df_clean = df_clean.dropna()

                                    if len(df_clean) > 0:
                                        # 清理临时文件
                                        os.remove(temp_file)

                                        print(f"✓ 成功获取日频GPR数据: {len(df_clean)} 条记录")
                                        print(f"  数据范围: {df_clean.index.min().strftime('%Y-%m-%d')} 到 {df_clean.index.max().strftime('%Y-%m-%d')}")
                                        print(f"  最新值: {df_clean['地缘政治风险指数'].iloc[-1]:.2f}")

                                        # 计算日频相关指标
                                        df_clean['地缘政治风险指数_日变化'] = df_clean['地缘政治风险指数'].diff()
                                        df_clean['地缘政治风险指数_日变化率'] = df_clean['地缘政治风险指数'].pct_change() * 100
                                        df_clean['地缘政治风险指数_5日均值'] = df_clean['地缘政治风险指数'].rolling(5).mean()
                                        df_clean['地缘政治风险指数_20日均值'] = df_clean['地缘政治风险指数'].rolling(20).mean()
                                        df_clean['地缘政治风险指数_历史分位数'] = df_clean['地缘政治风险指数'].rank(pct=True)

                                        return df_clean

                    except Exception as e:
                        print(f"    读取sheet '{sheet_name}'失败: {str(e)[:100]}...")
                        continue

                # 清理临时文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            except Exception as e:
                print(f"    Excel文件处理失败: {str(e)}")
                if os.path.exists(temp_file):
                    os.remove(temp_file)
        else:
            print(f"    访问失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"    错误: {str(e)[:100]}...")

    print("✗ 无法获取到有效的日频GPR数据")
    return pd.DataFrame()





def check_data_availability():
    """
    检查数据源可用性
    """
    print("检查数据源可用性...")

    # 检查网络连接
    try:
        response = requests.get("https://www.matteoiacoviello.com", timeout=10)
        if response.status_code == 200:
            print("✓ Matteo Iacoviello官方网站连接正常")
        else:
            print("⚠️ 官方网站连接异常")
    except:
        print("⚠️ 网络连接可能存在问题")

def save_daily_gpr_data(gpr_data):
    """
    保存日频地缘政治风险指数数据
    """
    print("\n正在保存数据...")

    if not gpr_data.empty:
        output_file = os.path.join(DATA_DIR, 'daily_geopolitical_risk_index.csv')
        gpr_data.to_csv(output_file, encoding='utf-8-sig')
        print(f"✓ 日频地缘政治风险指数数据已保存到: {output_file}")
        print(f"✓ 数据保存完成，共 {len(gpr_data)} 条记录")
        return True
    else:
        print("✗ 没有数据可保存")
        return False

def analyze_daily_gpr_data():
    """分析日频地缘政治风险指数数据"""
    print("\n" + "=" * 60)
    print("日频地缘政治风险指数分析")
    print("=" * 60)

    try:
        # 分析日频地缘政治风险指数
        gpr_file = os.path.join(DATA_DIR, 'daily_geopolitical_risk_index.csv')
        if os.path.exists(gpr_file):
            gpr_df = pd.read_csv(gpr_file, index_col=0, parse_dates=True)

            print(f"\n📊 日频地缘政治风险指数分析:")
            print(f"   数据期间: {gpr_df.index.min().strftime('%Y-%m-%d')} 至 {gpr_df.index.max().strftime('%Y-%m-%d')}")
            print(f"   数据频率: 日频")
            print(f"   总记录数: {len(gpr_df)} 条")

            # 分析地缘政治风险指数
            if '地缘政治风险指数' in gpr_df.columns:
                current_value = gpr_df['地缘政治风险指数'].iloc[-1]
                avg_value = gpr_df['地缘政治风险指数'].mean()
                std_value = gpr_df['地缘政治风险指数'].std()
                max_value = gpr_df['地缘政治风险指数'].max()
                min_value = gpr_df['地缘政治风险指数'].min()

                print(f"\n🎯 地缘政治风险指数:")
                print(f"   当前值: {current_value:.2f}")
                print(f"   历史平均: {avg_value:.2f}")
                print(f"   标准差: {std_value:.2f}")
                print(f"   历史最高: {max_value:.2f}")
                print(f"   历史最低: {min_value:.2f}")

                # 判断风险水平
                if current_value > avg_value + 2 * std_value:
                    print(f"   风险水平: 🔴 极高风险")
                elif current_value > avg_value + std_value:
                    print(f"   风险水平: 🟠 高风险")
                elif current_value > avg_value:
                    print(f"   风险水平: 🟡 偏高")
                elif current_value > avg_value - std_value:
                    print(f"   风险水平: 🟢 正常")
                else:
                    print(f"   风险水平: 🔵 低风险")

                # 与基准值100的比较
                if current_value > 100:
                    print(f"   vs基准值: 📈 高于历史平均水平 (+{current_value-100:.1f})")
                else:
                    print(f"   vs基准值: 📉 低于历史平均水平 ({current_value-100:.1f})")

                # 历史分位数
                if '地缘政治风险指数_历史分位数' in gpr_df.columns:
                    current_percentile = gpr_df['地缘政治风险指数_历史分位数'].iloc[-1] * 100
                    print(f"   历史分位数: {current_percentile:.1f}%")

                # 最新日变化
                if '地缘政治风险指数_日变化率' in gpr_df.columns:
                    recent_change = gpr_df['地缘政治风险指数_日变化率'].iloc[-1]
                    if not pd.isna(recent_change):
                        print(f"   日变化率: {recent_change:+.2f}%")

                # 移动平均分析
                if '地缘政治风险指数_5日均值' in gpr_df.columns and '地缘政治风险指数_20日均值' in gpr_df.columns:
                    ma5 = gpr_df['地缘政治风险指数_5日均值'].iloc[-1]
                    ma20 = gpr_df['地缘政治风险指数_20日均值'].iloc[-1]
                    print(f"   5日均值: {ma5:.2f}")
                    print(f"   20日均值: {ma20:.2f}")

                    if current_value > ma5 > ma20:
                        print(f"   短期趋势: 📈 上升")
                    elif current_value < ma5 < ma20:
                        print(f"   短期趋势: 📉 下降")
                    else:
                        print(f"   短期趋势: ➡️ 震荡")

            # 近期趋势分析
            if '地缘政治风险指数' in gpr_df.columns and len(gpr_df) >= 60:
                recent_30d = gpr_df['地缘政治风险指数'].iloc[-30:].mean()
                recent_7d = gpr_df['地缘政治风险指数'].iloc[-7:].mean()
                print(f"\n🔄 近期趋势:")
                print(f"   近30日平均: {recent_30d:.2f}")
                print(f"   近7日平均: {recent_7d:.2f}")

                if recent_7d > recent_30d * 1.05:
                    print(f"   趋势方向: � 风险上升")
                elif recent_7d < recent_30d * 0.95:
                    print(f"   趋势方向: 📉 风险下降")
                else:
                    print(f"   趋势方向: ➡️ 相对稳定")

    except Exception as e:
        print(f"   日频地缘政治风险指数分析失败: {e}")

def main():
    """主函数"""
    print("日频地缘政治风险指数(GPR)数据收集器")
    print("=" * 60)
    print("收集指标:")
    print("1. 地缘政治风险指数 (GPR) - 日频数据")
    print("=" * 60)

    print("数据来源: Matteo Iacoviello官方网站 (日频数据)")
    print()

    # 检查数据源可用性
    check_data_availability()
    print()

    # 收集日频地缘政治风险指数数据
    gpr_data = collect_daily_gpr_data()

    # 保存数据
    save_success = save_daily_gpr_data(gpr_data)

    # 显示数据收集总结
    print("\n" + "=" * 60)
    print("数据收集总结")
    print("=" * 60)

    if not gpr_data.empty:
        print(f"✓ 日频地缘政治风险指数: {len(gpr_data)} 条记录")
        print(f"  数据范围: {gpr_data.index.min().strftime('%Y-%m-%d')} 到 {gpr_data.index.max().strftime('%Y-%m-%d')}")
        success_count = 1
    else:
        print("✗ 日频地缘政治风险指数: 收集失败")
        success_count = 0

    print(f"\n成功收集 {success_count}/1 项数据")
    print(f"数据文件保存在: {DATA_DIR}")

    # 显示关键统计
    if not gpr_data.empty and '地缘政治风险指数' in gpr_data.columns:
        current_gpr = gpr_data['地缘政治风险指数'].iloc[-1]
        print(f"\n📊 关键指标:")
        print(f"   当前地缘政治风险指数: {current_gpr:.2f}")

    # 分析收集到的数据
    if save_success:
        analyze_daily_gpr_data()

    print("\n数据收集完成！")
    print("\n💡 使用建议:")
    print("- 日频GPR数据提供更高时间分辨率的地缘政治风险监控")
    print("- GPR指数上升通常伴随市场波动加剧")
    print("- 可用于高频交易策略和风险管理")
    print("- 结合技术分析指标可更好判断短期市场情绪变化")

if __name__ == "__main__":
    main()
