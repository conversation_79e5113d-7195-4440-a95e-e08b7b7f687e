#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于中美利差的沪深300择时策略

策略逻辑：
- 中美利差 = 中国10年期国债收益率 - 美国10年期国债收益率
- 当中美利差 > 过去3个月均值时，持有沪深300
- 当中美利差 <= 过去3个月均值时，空仓

作者：AI助手
日期：2025-01-29
"""

import pandas as pd
import numpy as np
import akshare as ak
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class ChinaUSSpreadTiming:
    def __init__(self, start_date='2015-01-01', initial_capital=100000):
        """
        初始化中美利差择时策略
        
        Args:
            start_date: 回测开始日期
            initial_capital: 初始资金
        """
        self.start_date = start_date
        self.initial_capital = initial_capital
        self.bond_data = None
        self.hs300_data = None
        self.merged_data = None
        self.backtest_results = None
        
    def get_bond_yield_data(self):
        """获取中美国债收益率数据"""
        print("正在获取中美国债收益率数据...")
        
        try:
            # 使用akshare获取中美国债收益率数据
            bond_data = ak.bond_zh_us_rate()
            
            if bond_data is None or bond_data.empty:
                raise Exception("未获取到债券收益率数据")
            
            # 数据预处理
            bond_data['日期'] = pd.to_datetime(bond_data['日期'])
            bond_data = bond_data.sort_values('日期')
            
            # 筛选需要的列
            required_columns = ['日期', '中国国债收益率10年', '美国国债收益率10年']
            missing_columns = [col for col in required_columns if col not in bond_data.columns]
            
            if missing_columns:
                print(f"缺少列: {missing_columns}")
                print(f"可用列: {list(bond_data.columns)}")
                raise Exception(f"数据中缺少必要的列: {missing_columns}")
            
            # 选择需要的列
            bond_data = bond_data[required_columns].copy()
            
            # 数据清理
            bond_data['中国国债收益率10年'] = pd.to_numeric(bond_data['中国国债收益率10年'], errors='coerce')
            bond_data['美国国债收益率10年'] = pd.to_numeric(bond_data['美国国债收益率10年'], errors='coerce')
            
            # 删除缺失值
            bond_data = bond_data.dropna()
            
            # 计算中美利差
            bond_data['中美利差'] = bond_data['中国国债收益率10年'] - bond_data['美国国债收益率10年']
            
            # 筛选时间范围
            bond_data = bond_data[bond_data['日期'] >= self.start_date]
            
            self.bond_data = bond_data
            print(f"成功获取到{len(bond_data)}条债券收益率数据")
            print(f"数据范围: {bond_data['日期'].min().strftime('%Y-%m-%d')} 至 {bond_data['日期'].max().strftime('%Y-%m-%d')}")
            
            return bond_data
            
        except Exception as e:
            print(f"获取债券收益率数据失败: {e}")
            raise
    
    def get_hs300_data(self):
        """获取沪深300指数数据"""
        print("正在获取沪深300指数数据...")
        
        try:
            # 使用akshare获取沪深300指数数据
            hs300_data = ak.stock_zh_index_daily(symbol="sh000300")
            
            if hs300_data is None or hs300_data.empty:
                raise Exception("未获取到沪深300指数数据")
            
            # 数据预处理
            hs300_data['date'] = pd.to_datetime(hs300_data['date'])
            hs300_data = hs300_data.sort_values('date')
            
            # 筛选时间范围
            hs300_data = hs300_data[hs300_data['date'] >= self.start_date]
            
            # 重命名列
            hs300_data = hs300_data.rename(columns={'date': '日期'})
            
            self.hs300_data = hs300_data
            print(f"成功获取到{len(hs300_data)}条沪深300指数数据")
            print(f"数据范围: {hs300_data['日期'].min().strftime('%Y-%m-%d')} 至 {hs300_data['日期'].max().strftime('%Y-%m-%d')}")
            
            return hs300_data
            
        except Exception as e:
            print(f"获取沪深300指数数据失败: {e}")
            raise
    
    def merge_data(self):
        """合并债券收益率和股指数据"""
        print("正在合并数据...")
        
        if self.bond_data is None:
            self.get_bond_yield_data()
        
        if self.hs300_data is None:
            self.get_hs300_data()
        
        # 使用merge_asof进行时间对齐
        merged_data = pd.merge_asof(
            self.hs300_data.sort_values('日期'),
            self.bond_data.sort_values('日期'),
            on='日期',
            direction='backward'
        )
        
        # 删除缺失值
        merged_data = merged_data.dropna()
        
        self.merged_data = merged_data
        print(f"合并后数据量: {len(merged_data)}条")
        
        return merged_data
    
    def calculate_timing_signals(self, lookback_days=90):
        """
        计算择时信号
        
        Args:
            lookback_days: 回望天数，默认90天（约3个月）
        """
        print(f"正在计算择时信号（回望期：{lookback_days}天）...")
        
        if self.merged_data is None:
            self.merge_data()
        
        data = self.merged_data.copy()
        
        # 计算中美利差的移动平均
        data['中美利差_MA'] = data['中美利差'].rolling(window=lookback_days, min_periods=1).mean()
        
        # 生成择时信号
        # 当中美利差 > 过去3个月均值时，持有沪深300（信号=1）
        # 当中美利差 <= 过去3个月均值时，空仓（信号=0）
        data['信号'] = (data['中美利差'] > data['中美利差_MA']).astype(int)
        
        # 计算信号变化
        data['信号变化'] = data['信号'].diff()
        
        self.merged_data = data
        
        # 统计信号分布
        signal_stats = data['信号'].value_counts()
        print(f"信号统计:")
        print(f"  持有期间: {signal_stats.get(1, 0)}天 ({signal_stats.get(1, 0)/len(data)*100:.1f}%)")
        print(f"  空仓期间: {signal_stats.get(0, 0)}天 ({signal_stats.get(0, 0)/len(data)*100:.1f}%)")
        
        return data
    
    def run_backtest(self):
        """运行回测"""
        print("正在运行回测...")
        
        if self.merged_data is None or '信号' not in self.merged_data.columns:
            self.calculate_timing_signals()
        
        data = self.merged_data.copy()
        
        # 计算日收益率
        data['hs300_return'] = data['close'].pct_change()
        
        # 计算策略收益率
        # 当信号为1时，获得沪深300收益率；当信号为0时，收益率为0
        data['strategy_return'] = data['hs300_return'] * data['信号'].shift(1)
        
        # 计算累计收益
        data['hs300_cumret'] = (1 + data['hs300_return'].fillna(0)).cumprod()
        data['strategy_cumret'] = (1 + data['strategy_return'].fillna(0)).cumprod()
        
        # 计算净值
        data['hs300_nav'] = data['hs300_cumret'] * self.initial_capital
        data['strategy_nav'] = data['strategy_cumret'] * self.initial_capital
        
        self.backtest_results = data
        
        # 计算绩效指标
        self.calculate_performance_metrics()
        
        return data
    
    def calculate_performance_metrics(self):
        """计算绩效指标"""
        if self.backtest_results is None:
            return
        
        data = self.backtest_results
        
        # 计算年化收益率
        total_days = (data['日期'].iloc[-1] - data['日期'].iloc[0]).days
        years = total_days / 365.25
        
        strategy_total_return = data['strategy_cumret'].iloc[-1] - 1
        hs300_total_return = data['hs300_cumret'].iloc[-1] - 1
        
        strategy_annual_return = (1 + strategy_total_return) ** (1/years) - 1
        hs300_annual_return = (1 + hs300_total_return) ** (1/years) - 1
        
        # 计算波动率
        strategy_volatility = data['strategy_return'].std() * np.sqrt(252)
        hs300_volatility = data['hs300_return'].std() * np.sqrt(252)
        
        # 计算夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        strategy_sharpe = (strategy_annual_return - risk_free_rate) / strategy_volatility
        hs300_sharpe = (hs300_annual_return - risk_free_rate) / hs300_volatility
        
        # 计算最大回撤
        strategy_rolling_max = data['strategy_cumret'].expanding().max()
        strategy_drawdown = (data['strategy_cumret'] - strategy_rolling_max) / strategy_rolling_max
        strategy_max_drawdown = strategy_drawdown.min()
        
        hs300_rolling_max = data['hs300_cumret'].expanding().max()
        hs300_drawdown = (data['hs300_cumret'] - hs300_rolling_max) / hs300_rolling_max
        hs300_max_drawdown = hs300_drawdown.min()
        
        # 打印结果
        print("\n" + "="*60)
        print("回测结果")
        print("="*60)
        print(f"回测期间: {data['日期'].iloc[0].strftime('%Y-%m-%d')} 至 {data['日期'].iloc[-1].strftime('%Y-%m-%d')}")
        print(f"回测天数: {len(data)}天 ({years:.1f}年)")
        print()
        print(f"{'指标':<20} {'中美利差择时策略':<15} {'沪深300买入持有':<15}")
        print("-" * 60)
        print(f"{'总收益率':<20} {strategy_total_return*100:>13.2f}% {hs300_total_return*100:>13.2f}%")
        print(f"{'年化收益率':<20} {strategy_annual_return*100:>13.2f}% {hs300_annual_return*100:>13.2f}%")
        print(f"{'年化波动率':<20} {strategy_volatility*100:>13.2f}% {hs300_volatility*100:>13.2f}%")
        print(f"{'夏普比率':<20} {strategy_sharpe:>13.2f} {hs300_sharpe:>13.2f}")
        print(f"{'最大回撤':<20} {strategy_max_drawdown*100:>13.2f}% {hs300_max_drawdown*100:>13.2f}%")
        print(f"{'最终净值':<20} {data['strategy_nav'].iloc[-1]:>13.0f} {data['hs300_nav'].iloc[-1]:>13.0f}")
        
        return {
            'strategy_annual_return': strategy_annual_return,
            'hs300_annual_return': hs300_annual_return,
            'strategy_volatility': strategy_volatility,
            'hs300_volatility': hs300_volatility,
            'strategy_sharpe': strategy_sharpe,
            'hs300_sharpe': hs300_sharpe,
            'strategy_max_drawdown': strategy_max_drawdown,
            'hs300_max_drawdown': hs300_max_drawdown
        }

    def plot_results(self):
        """绘制回测结果"""
        if self.backtest_results is None:
            print("请先运行回测")
            return

        data = self.backtest_results

        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        # 第一个子图：中美利差和移动平均
        ax1 = axes[0]
        ax1.plot(data['日期'], data['中美利差'], label='中美利差', color='blue', alpha=0.7)
        ax1.plot(data['日期'], data['中美利差_MA'], label='3个月移动平均', color='red', linewidth=2)
        ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        ax1.set_title('中美利差走势图', fontsize=14, fontweight='bold')
        ax1.set_ylabel('利差 (%)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 第二个子图：择时信号
        ax2 = axes[1]
        signal_colors = ['red' if x == 0 else 'green' for x in data['信号']]
        ax2.scatter(data['日期'], data['信号'], c=signal_colors, alpha=0.6, s=10)
        ax2.set_title('择时信号（绿色=持有，红色=空仓）', fontsize=14, fontweight='bold')
        ax2.set_ylabel('信号', fontsize=12)
        ax2.set_ylim(-0.1, 1.1)
        ax2.grid(True, alpha=0.3)

        # 第三个子图：净值走势
        ax3 = axes[2]
        ax3.plot(data['日期'], data['strategy_nav'], label='中美利差择时策略', color='red', linewidth=2)
        ax3.plot(data['日期'], data['hs300_nav'], label='沪深300买入持有', color='blue', linewidth=2)
        ax3.set_title('策略净值走势对比', fontsize=14, fontweight='bold')
        ax3.set_xlabel('日期', fontsize=12)
        ax3.set_ylabel('净值', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 格式化x轴日期
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.YearLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图表
        plt.savefig('plots/hs300_china_us_spread_timing.png', dpi=300, bbox_inches='tight')
        print("图表已保存至 plots/hs300_china_us_spread_timing.png")

        plt.show()

    def plot_drawdown(self):
        """绘制回撤图"""
        if self.backtest_results is None:
            print("请先运行回测")
            return

        data = self.backtest_results

        # 计算回撤
        strategy_rolling_max = data['strategy_cumret'].expanding().max()
        strategy_drawdown = (data['strategy_cumret'] - strategy_rolling_max) / strategy_rolling_max * 100

        hs300_rolling_max = data['hs300_cumret'].expanding().max()
        hs300_drawdown = (data['hs300_cumret'] - hs300_rolling_max) / hs300_rolling_max * 100

        # 绘制回撤图
        plt.figure(figsize=(15, 6))
        plt.fill_between(data['日期'], strategy_drawdown, 0, alpha=0.3, color='red', label='中美利差择时策略')
        plt.fill_between(data['日期'], hs300_drawdown, 0, alpha=0.3, color='blue', label='沪深300买入持有')
        plt.plot(data['日期'], strategy_drawdown, color='red', linewidth=1)
        plt.plot(data['日期'], hs300_drawdown, color='blue', linewidth=1)

        plt.title('策略回撤对比', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('回撤 (%)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 格式化x轴
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.gca().xaxis.set_major_locator(mdates.YearLocator())
        plt.xticks(rotation=45)

        plt.tight_layout()

        # 保存图表
        plt.savefig('plots/hs300_china_us_spread_drawdown.png', dpi=300, bbox_inches='tight')
        print("回撤图已保存至 plots/hs300_china_us_spread_drawdown.png")

        plt.show()


def main():
    """主函数"""
    print("="*60)
    print("基于中美利差的沪深300择时策略回测")
    print("="*60)

    # 创建策略实例
    strategy = ChinaUSSpreadTiming(start_date='2015-01-01', initial_capital=100000)

    try:
        # 获取数据
        print("\n1. 获取数据...")
        strategy.get_bond_yield_data()
        strategy.get_hs300_data()
        strategy.merge_data()

        # 计算择时信号
        print("\n2. 计算择时信号...")
        strategy.calculate_timing_signals(lookback_days=90)  # 3个月回望期

        # 运行回测
        print("\n3. 运行回测...")
        strategy.run_backtest()

        # 绘制结果
        print("\n4. 绘制结果...")
        import os
        os.makedirs('plots', exist_ok=True)
        strategy.plot_results()
        strategy.plot_drawdown()

        print("\n回测完成！")

    except Exception as e:
        print(f"回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
